#!/usr/bin/env node

/**
 * Simple test script to verify blur detection logic works
 * This tests the core entropy calculation and heuristic logic
 */

// Mock the React Native dependencies for testing
const mockRNFS = {
  stat: async (path) => ({
    size: Math.floor(Math.random() * 200000) + 50000 // Random file size between 50KB-250KB
  }),
  readFile: async (path, encoding) => {
    // Generate mock base64 data with varying entropy
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    let mockData = '';
    const length = Math.floor(Math.random() * 5000) + 1000; // 1KB-6KB sample
    
    for (let i = 0; i < length; i++) {
      mockData += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return mockData;
  }
};

// Mock the thresholds
const SHARPNESS_THRESHOLDS = {
  VERY_BLURRY: 50,
  BLURRY: 100,
  ACCEPTABLE: 150,
  SHARP: 200
};

/**
 * Calculate entropy of a string (copied from imageSharpness.ts)
 */
const calculateStringEntropy = (str) => {
  const frequencies = {};
  
  // Count character frequencies
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    frequencies[char] = (frequencies[char] || 0) + 1;
  }
  
  // Calculate entropy
  let entropy = 0;
  const length = str.length;
  
  for (const char in frequencies) {
    const probability = frequencies[char] / length;
    entropy -= probability * Math.log2(probability);
  }
  
  return entropy;
};

/**
 * Test implementation of the heuristic blur detection
 */
const testBlurDetection = async (imagePath) => {
  try {
    // Get file stats for basic analysis
    const fileStats = await mockRNFS.stat(imagePath);
    const fileSize = fileStats.size;
    
    // Read a sample of the file for entropy analysis
    const sampleSize = Math.min(10000, fileSize);
    const base64Sample = await mockRNFS.readFile(imagePath, 'base64');
    const sampleData = base64Sample.slice(0, sampleSize);
    
    // Calculate entropy of the sample data
    const entropy = calculateStringEntropy(sampleData);
    
    // Estimate sharpness based on file size, entropy, and other heuristics
    let estimatedSharpness = 0;
    
    // File size factor: larger files often indicate more detail/sharpness
    const sizeFactor = Math.min(fileSize / 100000, 2); // Normalize to 0-2 range
    
    // Entropy factor: higher entropy often indicates more detail
    const entropyFactor = entropy * 50;
    
    // Base64 data characteristics
    const compressionRatio = base64Sample.length / fileSize;
    const compressionFactor = Math.max(0, (1.5 - compressionRatio) * 100);
    
    estimatedSharpness = sizeFactor * 50 + entropyFactor + compressionFactor;
    
    // Clamp to reasonable range
    estimatedSharpness = Math.max(10, Math.min(500, estimatedSharpness));
    
    const isBlurry = estimatedSharpness < SHARPNESS_THRESHOLDS.BLURRY;

    let confidence;
    if (
      estimatedSharpness < SHARPNESS_THRESHOLDS.VERY_BLURRY ||
      estimatedSharpness > SHARPNESS_THRESHOLDS.SHARP
    ) {
      confidence = 'high';
    } else if (estimatedSharpness < SHARPNESS_THRESHOLDS.ACCEPTABLE) {
      confidence = 'medium';
    } else {
      confidence = 'low';
    }

    return {
      sharpnessScore: Math.round(estimatedSharpness * 100) / 100,
      isBlurry,
      confidence,
      debug: {
        fileSize,
        entropy: Math.round(entropy * 100) / 100,
        sizeFactor: Math.round(sizeFactor * 100) / 100,
        entropyFactor: Math.round(entropyFactor * 100) / 100,
        compressionFactor: Math.round(compressionFactor * 100) / 100
      }
    };
  } catch (error) {
    console.error('Error analyzing image:', error);
    return {
      sharpnessScore: 0,
      isBlurry: true,
      confidence: 'low'
    };
  }
};

// Run tests
async function runTests() {
  console.log('🧪 Testing Blur Detection Heuristic Implementation\n');
  
  const testCases = [
    'test-image-1.jpg',
    'test-image-2.jpg', 
    'test-image-3.jpg',
    'test-image-4.jpg',
    'test-image-5.jpg'
  ];
  
  for (const testCase of testCases) {
    console.log(`📸 Testing: ${testCase}`);
    const result = await testBlurDetection(testCase);
    
    console.log(`   Sharpness Score: ${result.sharpnessScore}`);
    console.log(`   Is Blurry: ${result.isBlurry ? '❌ Yes' : '✅ No'}`);
    console.log(`   Confidence: ${result.confidence}`);
    
    if (result.debug) {
      console.log(`   Debug Info:`);
      console.log(`     File Size: ${result.debug.fileSize} bytes`);
      console.log(`     Entropy: ${result.debug.entropy}`);
      console.log(`     Size Factor: ${result.debug.sizeFactor}`);
      console.log(`     Entropy Factor: ${result.debug.entropyFactor}`);
      console.log(`     Compression Factor: ${result.debug.compressionFactor}`);
    }
    
    console.log('');
  }
  
  console.log('✅ All tests completed successfully!');
  console.log('\n📝 Note: This is using heuristic-based detection.');
  console.log('   For pixel-based analysis, install jpeg-js dependency.');
}

runTests().catch(console.error);
