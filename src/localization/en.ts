const en = {
  // Navigation
  routes: 'Routes',
  route: 'Route',
  dispatch: 'Dispatch',
  profile: 'Profile',
  cancel: 'Cancel',
  close: 'Close',
  refresh: 'Refresh',
  dropoff: 'Delivery',
  confirm: 'Confirm',

  // Login screen
  email: 'Email',
  password: 'Password',
  login: 'Log in',
  forgot_password: 'Forgot password?',
  terms: 'By signing in you accept our ',
  privacy_policy: 'Privacy Policy',

  // Route overview screen
  todays_routes: "Today's Routes",
  current: 'Current',
  upcoming: 'Upcoming',
  see_route_details: 'See route details',
  active: 'Active',
  completed: 'Completed',
  complete: 'Complete',
  no_active_routes: 'No active routes available',
  no_completed_routes: 'No routes completed yet',
  stops: 'stops',
  no_start_time_set: 'No start time',

  // Profile screen
  user_information: 'User information',
  name: 'Name',
  org_id: 'Org. ID',
  user_id: 'User. ID',
  device_information: 'Device information',
  manufacturer: 'Manufacturer',
  model: 'Model',
  os_version: 'OS version',
  app_version: 'App version',
  free_storage: 'Free storage',

  unknown: 'Unknown',
  logout: 'Log out',
  logout_with_unsaved_data: 'Data not synced',
  logout_info: "You'll log out {{name}}",
  logout_warning:
    'You have unsynced data that needs to be uploaded. Stay connected to a stable network to allow the sync to complete. Logging out before syncing may result in data loss',

  // Route detail screen
  nextStop: 'Next stop',
  lastCompletedStop: 'Last completed stop',
  navigateToStop: 'Navigate to stop',
  completedRoute: 'Completed route',
  scheduledRoute: 'Scheduled route',
  canceledRoute: 'Canceled route',
  arrivedAtStop: 'Arrived at {{stopName}}',
  waitForMinutes: 'Wait for {{minutes}} min',
  closureWarningTitle:
    '{{stopName}} was completed less than {{minutes}} minutes ago',
  closureWarningMessage:
    'You must wait at least {{minutes}} minutes between stops. Please wait until the timer has elapsed to mark next stop as arrived',
  proof_of_service_missing: 'Complete proof of service to continue',

  // Collect Signature screen
  sign_below: 'Sign below',
  add_comment: 'Comment (optional)',
  additional_information: 'Any additional information',
  complete_dropoff: 'Complete delivery',
  collect_signature: 'Collect signature',

  // Schedule screens
  daily_schedule: 'Daily schedule',
  stop_details: 'Stop details',
  select_a_service: 'Select a service',
  weekly_schedule: 'Weekly schedule',
  select_date: 'Select date below',
  select_date_subtitle: 'Select a date to view daily schedule',
  see_schedule: 'See schedule',
  no_tasks_assigned: 'No tasks assigned',
  no_tasks_assigned_details:
    'You currently have no assigned tasks for the selected date. Refresh or check back later',
  no_completed_tasks: 'No completed tasks to show',
  see_stop_details: 'See stop details',
  select_service_title: 'Select a service to complete',
  select_service_subtitle:
    'All actions must be completed to mark this stop as done',
  select_service_continue: 'Select a service to continue',
  select_service_continue_to: 'Continue to {{task}}',

  // Stop detail screen
  getting_there: 'Getting there',
  things_to_know: 'Things to know',
  tasks: 'Tasks',

  // Stop overview screen
  address: 'Address',
  notes: 'Notes',
  contact: 'Contact',
  parcels_expected: 'Parcels expected',
  pickup_parcels: 'Pickup parcels',
  delivery_parcels: 'Delivery parcels',
  add_new: 'Add new',
  add_parcels_manually: 'Add parcels manually',
  service_photo_proof: 'Proof of service photo',
  photos_taken: 'Photos taken',
  take_new: 'Take new',
  take_photo: 'Take a photo',
  photos_taken_appear_here: 'Photos taken will appear here',
  comments_optional: 'Comment (optional)',
  complete_pickup: 'Complete pickup',
  no_notes_available: 'No notes available',
  stop_overview: 'Stop overview',
  delivery: 'Delivery',
  pickup: 'Pickup',
  exchange: 'Exchange',
  service: 'Service',
  address_copied: 'Address copied to clipboard!',
  stop_update_failed: 'Failed to update stop',
  stop_failed_to_queue: 'Failed to queue stop',
  parcels: 'Parcels',
  delivery_destination: 'Delivery destination',
  barcode_not_matched: 'Barcode not matched',
  barcode_not_matched_description:
    'Barcode scanned does not match the parcel selected. Select the correct parcel or scan barcode',

  // Add/Edit Parcel
  parcel_information: 'Parcel information',
  scan_barcode_info: 'Hold your phone over the barcode',
  scan_barcode_title: 'Scan barcode',
  barcode: 'Barcode',
  select_parcel_type: 'Select type',
  search_parcel_type: 'Search type',
  type: 'Type',
  declare_parcel: 'Declare parcel',
  type_label: 'Type',
  quantity_label: 'Quantity',
  number_of_parcels: 'Number of parcels',
  sort_by: 'Sort by',
  continue: 'Continue',
  dropoff_destination: 'Delivery destination',
  select_dropoff_destination: 'Select delivery destination',
  signature_required: 'Signature required?',
  quantity: 'Quantity',
  parcel_proof_of_service: 'Parcel proof of service',
  take_parcel_photo: 'Parcel photo',
  yes: 'Yes',
  no: 'No',
  add_a_parcel: 'Add a parcel',
  edit_parcel: 'Edit parcel',
  save: 'Save',
  save_parcel: 'Save parcel',
  no_result_found: 'No result found',
  pending_scan: 'Pending scan',
  scan_barcode: 'Scan barcode',
  confirm_parcel: 'Confirm parcel',

  // work orders
  complete_pickup_tasks_to_continue: 'Complete pickup tasks to continue',
  complete_delivery_tasks_to_continue: 'Complete delivery tasks to continue',
  complete_photo_to_continue: 'Complete photo to continue',
  no_contact_listed: 'No contact listed',

  // utils
  invalidData: 'Invalid data',
  warning: 'Warning',
  open_settings: 'Open settings',
  unable_to_open_settings: 'Unable to open settings',
  locationPermissions: 'Location permissions',
  cameraPermissionDesc:
    'Camera access is required to complete your tasks. Please enable it in your app settings',
  locationPermissionDescIOS:
    'Location access is required for accurate tracking. Please select "Always" and enable "Precise location" in your app settings',
  locationPermissionDescAndroid:
    'Location access is required for accurate tracking. Please select "Allow all the time" and enable "Use precise location" in your app settings',
  motionPermissionDescIOS:
    'Motion & Fitness access is required for optimal performance. Without it, your battery may drain faster. Please enable it in your app settings',
  motionPermissionDescAndroid:
    'Physical Activity access is required for optimal performance. Without it, your battery may drain faster. Please select "Allow" in your app settings',
  unableToFetchLocation: 'Unable to fetch your current location.',

  // Driver actions
  already_marked_arrived: 'You already marked this stop as arrived',
  must_arrive_first: 'You must be arrived at this stop first',
  already_marked_completed: 'You already marked this stop as completed',
  stop_not_found: 'Stop not found',
  route_not_found: 'Route not found',
  invalid_parameters: 'Invalid parameters',
  invalid_route_summery_id: 'Invalid route summery Id',
  stop_updated: 'Stop {{field}} updated',
  route_updated: 'Route {{field}} updated',
  parcel_saved: 'Parcel saved',
  parcel_not_found: 'Parcel not found',
  parcel_updated: 'Parcel updated',
  parcel_deleted: 'Parcel deleted',
  parcel_create_failed: 'Failed to create parcel',
  parcel_update_failed: 'Failed to update parcel',
  parcel_delete_failed: 'Failed to delete parcel',
  no_parcels_picked_at_stop: 'No parcels picked at this stop',
  parcels_created: '{{count}} parcels created at stop {{stopId}}',
  images_request_created: '{{count}} images created at stop {{stopId}}',

  // Bottom sheet
  sort: 'Sort',
  invalid_date: 'Invalid date',
  title_a_to_z: 'A to Z',
  subtitle_sorted_alphabetical: 'Sorted in alphabetical order',
  title_z_to_a: 'Z to A',
  subtitle_sorted_reverse_alphabetical: 'Sorted in reverse alphabetical order',
  title_next_available_stops: 'Next available stops',
  subtitle_sorted_closest_time: 'Sorted by closest time to current time',

  // Confirmation screen text
  confirmation_screen: 'Confirmation screen',
  route_completed: 'Route completed',
  pickup_completed: 'Pickup completed',
  dropoff_completed: 'Delivery completed',
  sub_pickup_continue_route: 'Continue with your route',
  sub_pickup_continue_dropoff: 'Continue with delivery',
  sub_pickup_sno: 'Samples not out (SNO)',
  sub_pickup_no_confirm: 'SNO - No pickup confirmed',
  sub_dropoff_continue_route: 'Continue with your route',
  sub_dropoff_continue_pickup: 'Continue with the pickup',
  great_job: 'Great job!',
  stop_completed: 'Stop completed',

  // camera view screen
  retake: 'Retake',
  re_scan: 'Rescan',
  submit: 'Submit',
  scanning: 'Scanning...',
  loading: 'Loading...',
  error: 'Error',
  failed_to_take_photo: 'Failed to take photo: {{error}}',
  unexpected_error_occurred_while_capturing_the_photo:
    'An unexpected error occurred while capturing the photo.',
  photo_view: 'Photo viewer',
  take_a_proof_of_service_photo: 'Take a proof of service photo',
  take_a_parcel_photo: 'Take a parcel photo',

  // Image manager
  image_invalid_payload: 'Invalid image payload',
  image_upload_failed: 'Failed to upload image',
  image_delete_failed: 'Failed to delete image',
  image_not_found: 'Image not found',
  image_add_update_success: 'Image added/updated successfully',
  image_add_update_failed: 'Failed to add/update image',

  // Errors
  error_generic: 'Something unexpected happened! Please try again later',
  error_auth_failure:
    'Incorrect credentials. Please make sure your email and password are correct',
  error_no_network:
    'No network detected. Please check your network and try again',

  // Database
  database_connection_failed: 'Database connection failed',

  // Protocol
  update_failed: 'Failed to update the route summary',

  delete_parcel: 'Delete [{quantity} {type}]',
  delete_parcel_description:
    'This action cannot be recovered, if deleted it will be deleted for good',
  delete: 'Delete',

  // Notifications
  notification_permission: 'Notification permission',
  push_notifications_disabled:
    'Push notifications are disabled. Please allow push notifications in your phone settings for Rapid Medical app',
  push_notifications_limited: 'Push notifications are limited on this device.',
  push_notifications_unavailable:
    'Push notifications are unavailable on this device.',
  protocol_completed: 'Protocol verification completed',
};

export default en;
