import { ImageSchema } from '~/db/realm/schemas/image.schema';
import en from '~/localization/en';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ImageTitleInfo } from '~/utils/images';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { getCurrentLocationFromGeolocation } from '~/services/location/LocationService';

export type ImageTitleType = keyof typeof ImageTitleInfo;

export type AddImagePayload = {
  titleType: ImageTitleType;
  base64String: string;
  stopId?: string;
  routeSummaryId: string;
  parcelId?: string;
  dailyScheduleId?: string;
  workOrderId?: string;
  isBlurry?: boolean;
  sharpnessScore?: number;
};

const validateImagePayload = (payload: AddImagePayload): boolean => {
  if (!payload.titleType || !payload.base64String) {
    return false;
  }

  const hasAtLeastOneId = !!(
    payload.routeSummaryId ||
    payload.stopId ||
    payload.dailyScheduleId
  );

  return hasAtLeastOneId;
};

const generateImageId = ({
  titleType,
  stopId,
  parcelId,
  routeSummaryId,
  dailyScheduleId,
  workOrderId,
}: AddImagePayload) => {
  const titleInfo = ImageTitleInfo[titleType];
  const id = [
    routeSummaryId,
    stopId,
    parcelId,
    dailyScheduleId,
    workOrderId,
    titleInfo.title,
    titleInfo.multiple ? Date.now() : null,
  ]
    .filter(Boolean)
    .join(':');

  return id;
};

const getFilterQuery = (stopId?: string, parcelId?: string): string => {
  let query = `Title == $0 AND RouteSummaryId == $1`;
  if (stopId) query += ' AND StopId == $2';
  if (parcelId) query += ` AND ParcelId == $${stopId ? '3' : '2'}`;
  return query;
};

const getFilterParams = (
  title: string,
  routeSummaryId: string,
  stopId?: string,
  parcelId?: string,
): unknown[] => {
  const params: unknown[] = [title, routeSummaryId];
  if (stopId) params.push(stopId);
  if (parcelId) params.push(parcelId);
  return params;
};

async function addImage(payload: AddImagePayload) {
  try {
    const realm = await getRealmInstance();

    if (!realm || realm.isClosed) {
      return { success: false, message: en.image_add_update_failed };
    }

    if (!validateImagePayload(payload)) {
      return { success: false, message: en.image_invalid_payload };
    }

    const id = generateImageId(payload);
    const location = await getCurrentLocationFromGeolocation();

    safeWrite(realm, () => {
      realm.create(
        ImageSchema.name,
        {
          Id: id,
          Title: ImageTitleInfo[payload.titleType].title,
          VersionData: payload.base64String,
          StopId: payload.stopId,
          RouteSummaryId: payload.routeSummaryId,
          DailyScheduleId: payload.dailyScheduleId,
          ParcelId: payload.parcelId,
          WorkOrderId: payload.workOrderId ?? undefined,
          PathOnClient: `${id}.jpg`,
          Coordinates__Latitude__s: location?.coords?.latitude ?? undefined,
          Coordinates__Longitude__s: location?.coords?.longitude ?? undefined,
          IsBlurry: payload.isBlurry ?? undefined,
          SharpnessScore: payload.sharpnessScore ?? undefined,
        },
        true,
      );
    });
    return { success: true, message: en.image_add_update_success };
  } catch (error) {
    console.error('image-manager.ts: addImage:', error);
    return { success: false, message: en.image_add_update_failed };
  }
}

async function deleteImage(id: string) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed)
      return { success: false, message: en.image_not_found };
    const image = realm.objectForPrimaryKey(ImageSchema.name, id);
    if (!image) {
      return { success: false, message: en.image_not_found };
    }
    safeWrite(realm, () => {
      realm.delete(image);
    });
    return { success: true };
  } catch (error) {
    console.error('image-manager.ts: deleteImage:', error);
    return { success: false, message: en.image_delete_failed };
  }
}

async function deleteImages({
  titleType,
  stopId,
  routeSummaryId,
  parcelId,
}: {
  titleType: ImageTitleType;
  stopId?: string;
  routeSummaryId: string;
  parcelId?: string;
}) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed)
      return { success: false, message: en.image_not_found };
    const title = ImageTitleInfo[titleType].title;

    const filtered = realm
      .objects(ImageSchema.name)
      .filtered(
        getFilterQuery(stopId, parcelId),
        ...getFilterParams(title, routeSummaryId, stopId, parcelId),
      );

    if (filtered.length === 0) {
      return { success: false, message: en.image_not_found };
    }

    safeWrite(realm, () => {
      realm.delete(filtered);
    });

    return { success: true };
  } catch (error) {
    console.error('image-manager.ts: deleteImages:', error);
    return { success: false, message: en.image_delete_failed };
  }
}

export const ImageStore = {
  addImage,
  deleteImage,
  deleteImages,
};
