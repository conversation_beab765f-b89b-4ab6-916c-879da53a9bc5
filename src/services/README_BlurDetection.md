# BlurDetectionService

A service for detecting blur in static photos using Laplacian variance analysis. This service provides an easy-to-use interface for analyzing image quality and determining if photos are blurry.

## Features

- ✅ **File Path Analysis**: Analyze images directly from file paths
- ✅ **Base64 Analysis**: Analyze images from base64 encoded data
- ✅ **Confidence Levels**: Get confidence ratings (low, medium, high) for blur detection
- ✅ **Human-Readable Descriptions**: Get descriptive text about image quality
- ✅ **Easy Integration**: Simple API that fits into existing camera workflows
- ✅ **Error Handling**: Graceful error handling with fallback results
- ✅ **Comprehensive Testing**: Full test suite included

## Quick Start

```typescript
import { BlurDetectionService } from '~/services/BlurDetectionService';

// Analyze an image from file path
const result = await BlurDetectionService.analyzeFromPath('/path/to/image.jpg');
console.log(`Is blurry: ${result.isBlurry}, Score: ${result.sharpnessScore}`);

// Quick check if image is acceptable
const isGood = await BlurDetectionService.isImageAcceptable('/path/to/image.jpg');
console.log(`Image quality is acceptable: ${isGood}`);

// Get human-readable description
const description = BlurDetectionService.getQualityDescription(result);
console.log(description); // "Image is sharp and clear (score: 250)"
```

## API Reference

### `analyzeFromPath(imagePath: string, options?: BlurDetectionOptions)`

Analyzes blur from an image file path.

**Parameters:**
- `imagePath`: Path to the image file
- `options`: Optional configuration object
  - `verbose`: Boolean to enable detailed logging

**Returns:** `Promise<BlurDetectionResult>`

### `analyzeFromBase64(base64Data: string, options?: BlurDetectionOptions)`

Analyzes blur from base64 image data.

**Parameters:**
- `base64Data`: Base64 encoded image string
- `options`: Optional configuration object

**Returns:** `Promise<BlurDetectionResult>`

### `isImageAcceptable(imagePath: string, options?: BlurDetectionOptions)`

Quick check if an image has acceptable quality (not blurry).

**Returns:** `Promise<boolean>`

### `isBase64ImageAcceptable(base64Data: string, options?: BlurDetectionOptions)`

Quick check if base64 image data has acceptable quality.

**Returns:** `Promise<boolean>`

### `getQualityDescription(result: BlurDetectionResult)`

Gets a human-readable description of the blur analysis result.

**Returns:** `string`

## Result Object

```typescript
interface BlurDetectionResult {
  sharpnessScore: number;      // Higher = sharper (0-500+ range)
  isBlurry: boolean;           // True if image is considered blurry
  confidence: 'low' | 'medium' | 'high';  // Confidence in the result
  analyzedAt: Date;            // When analysis was performed
  analysisMethod: 'file_path' | 'base64';  // Method used for analysis
}
```

## Sharpness Score Thresholds

- **< 50**: Very blurry (high confidence)
- **50-100**: Blurry (medium-high confidence)
- **100-200**: Acceptable quality (medium confidence)
- **200-400**: Good quality (medium-high confidence)
- **> 400**: Sharp/excellent quality (high confidence)

## Integration Examples

### Camera Screen Integration

The service is already integrated into `CameraViewScreen.tsx`. When a photo is taken, it automatically analyzes the image and logs the results:

```typescript
// In CameraViewScreen.tsx (already implemented)
const blurResult = await BlurDetectionService.analyzeFromPath(photo.path, { verbose: true });
console.log('Blur detection result:', blurResult);

if (blurResult.isBlurry && blurResult.confidence !== 'low') {
  const qualityDescription = BlurDetectionService.getQualityDescription(blurResult);
  console.warn('Photo quality warning:', qualityDescription);
}
```

### Custom Integration

```typescript
// In your component
import { BlurDetectionService } from '~/services/BlurDetectionService';

const handlePhotoCapture = async (photoPath: string) => {
  try {
    const blurResult = await BlurDetectionService.analyzeFromPath(photoPath);
    
    if (blurResult.isBlurry) {
      // Show warning to user
      Alert.alert(
        'Photo Quality Warning',
        BlurDetectionService.getQualityDescription(blurResult),
        [
          { text: 'Keep Photo', onPress: () => acceptPhoto(photoPath) },
          { text: 'Retake', onPress: () => retakePhoto() }
        ]
      );
    } else {
      // Photo is good, proceed
      acceptPhoto(photoPath);
    }
  } catch (error) {
    console.error('Blur detection failed:', error);
    // Proceed with photo anyway
    acceptPhoto(photoPath);
  }
};
```

## Testing

### Unit Tests

Run the comprehensive test suite:

```bash
# Run blur detection tests
yarn test --testPathPattern=BlurDetectionService.test.ts
```

### Manual Testing

Use the test utilities for manual testing:

```typescript
import { testBlurDetectionWithPath, runBlurDetectionTests } from '~/utils/testBlurDetection';

// Test with a specific image
await testBlurDetectionWithPath('/path/to/your/test/image.jpg');

// Run comprehensive test suite
await runBlurDetectionTests();
```

### Testing in Development

1. **Console Logging**: The service is integrated with verbose logging in the camera screen
2. **Test Images**: Use the test utilities to validate with known sharp/blurry images
3. **Real Device Testing**: Take photos with your device and check console logs

## Technical Details

- **Algorithm**: Uses Laplacian variance for edge detection
- **Implementation**: Built on top of existing `imageSharpness.ts` utilities
- **Performance**: Optimized for mobile use with image compression
- **Error Handling**: Graceful degradation with sensible defaults
- **Memory**: Efficient processing with minimal memory footprint

## Troubleshooting

### Common Issues

1. **"File not found" errors**: Ensure image paths are correct and files exist
2. **Base64 decode errors**: Verify base64 data is valid image data
3. **Low confidence results**: Normal for borderline cases, consider user confirmation

### Debug Tips

1. Enable verbose logging: `{ verbose: true }`
2. Check console logs for detailed analysis information
3. Use test utilities to validate with known images
4. Verify image file formats are supported (JPEG recommended)

## Future Enhancements

- [ ] OpenCV integration for improved accuracy
- [ ] Real-time blur detection during camera preview
- [ ] Custom threshold configuration
- [ ] Batch image processing
- [ ] Performance optimizations for large images
