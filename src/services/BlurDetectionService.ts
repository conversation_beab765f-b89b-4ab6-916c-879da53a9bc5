import { analyzeImageSharpness, analyzeImageSharpnessFromBase64, SharpnessResult } from '~/utils/imageSharpness';

/**
 * BlurDetectionService
 * 
 * A service for detecting blur in static photos. This service can analyze images
 * from file paths or base64 data and determine if they are blurry using Laplacian
 * variance analysis.
 * 
 * This service is designed to be easily testable by passing images into it,
 * and can be integrated into the camera photo capture flow.
 */

export interface BlurDetectionResult extends SharpnessResult {
  /** Timestamp when the analysis was performed */
  analyzedAt: Date;
  /** The method used for analysis */
  analysisMethod: 'file_path' | 'base64';
}

export interface BlurDetectionOptions {
  /** Whether to log detailed analysis information */
  verbose?: boolean;
}

class BlurDetectionServiceClass {
  /**
   * Analyze blur from an image file path
   * @param imagePath - Path to the image file
   * @param options - Optional configuration
   * @returns Promise resolving to blur detection result
   */
  async analyzeFromPath(
    imagePath: string,
    options: BlurDetectionOptions = {}
  ): Promise<BlurDetectionResult> {
    try {
      if (options.verbose) {
        console.log(`BlurDetectionService: Analyzing image at path: ${imagePath}`);
      }

      const sharpnessResult = await analyzeImageSharpness(imagePath);
      
      const result: BlurDetectionResult = {
        ...sharpnessResult,
        analyzedAt: new Date(),
        analysisMethod: 'file_path',
      };

      if (options.verbose) {
        console.log('BlurDetectionService: Analysis complete', {
          sharpnessScore: result.sharpnessScore,
          isBlurry: result.isBlurry,
          confidence: result.confidence,
        });
      }

      return result;
    } catch (error) {
      console.error('BlurDetectionService: Error analyzing image from path:', error);
      return {
        sharpnessScore: 0,
        isBlurry: true,
        confidence: 'low',
        analyzedAt: new Date(),
        analysisMethod: 'file_path',
      };
    }
  }

  /**
   * Analyze blur from base64 image data
   * @param base64Data - Base64 encoded image data
   * @param options - Optional configuration
   * @returns Promise resolving to blur detection result
   */
  async analyzeFromBase64(
    base64Data: string,
    options: BlurDetectionOptions = {}
  ): Promise<BlurDetectionResult> {
    try {
      if (options.verbose) {
        console.log(`BlurDetectionService: Analyzing base64 image (${base64Data.length} chars)`);
      }

      const sharpnessResult = await analyzeImageSharpnessFromBase64(base64Data);
      
      const result: BlurDetectionResult = {
        ...sharpnessResult,
        analyzedAt: new Date(),
        analysisMethod: 'base64',
      };

      if (options.verbose) {
        console.log('BlurDetectionService: Analysis complete', {
          sharpnessScore: result.sharpnessScore,
          isBlurry: result.isBlurry,
          confidence: result.confidence,
        });
      }

      return result;
    } catch (error) {
      console.error('BlurDetectionService: Error analyzing image from base64:', error);
      return {
        sharpnessScore: 0,
        isBlurry: true,
        confidence: 'low',
        analyzedAt: new Date(),
        analysisMethod: 'base64',
      };
    }
  }

  /**
   * Check if an image is acceptable quality (not blurry)
   * @param imagePath - Path to the image file
   * @param options - Optional configuration
   * @returns Promise resolving to boolean indicating if image is acceptable
   */
  async isImageAcceptable(
    imagePath: string,
    options: BlurDetectionOptions = {}
  ): Promise<boolean> {
    const result = await this.analyzeFromPath(imagePath, options);
    return !result.isBlurry;
  }

  /**
   * Check if base64 image data is acceptable quality (not blurry)
   * @param base64Data - Base64 encoded image data
   * @param options - Optional configuration
   * @returns Promise resolving to boolean indicating if image is acceptable
   */
  async isBase64ImageAcceptable(
    base64Data: string,
    options: BlurDetectionOptions = {}
  ): Promise<boolean> {
    const result = await this.analyzeFromBase64(base64Data, options);
    return !result.isBlurry;
  }

  /**
   * Get a human-readable description of the blur analysis result
   * @param result - The blur detection result
   * @returns A descriptive string about the image quality
   */
  getQualityDescription(result: BlurDetectionResult): string {
    const { sharpnessScore, isBlurry, confidence } = result;
    
    if (isBlurry) {
      if (confidence === 'high') {
        return `Image is clearly blurry (score: ${sharpnessScore})`;
      } else if (confidence === 'medium') {
        return `Image appears blurry (score: ${sharpnessScore})`;
      } else {
        return `Image may be blurry, but confidence is low (score: ${sharpnessScore})`;
      }
    } else {
      if (confidence === 'high') {
        return `Image is sharp and clear (score: ${sharpnessScore})`;
      } else if (confidence === 'medium') {
        return `Image appears acceptable (score: ${sharpnessScore})`;
      } else {
        return `Image quality is uncertain (score: ${sharpnessScore})`;
      }
    }
  }
}

// Export a singleton instance
export const BlurDetectionService = new BlurDetectionServiceClass();
