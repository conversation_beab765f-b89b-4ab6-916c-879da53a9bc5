import { compressImageFromUri, convertFileToBase64 } from '~/utils/images';
import { analyzeImageSharpness, SharpnessResult } from '~/utils/imageSharpness';
import { ImageStore, AddImagePayload } from '~/services/ImageStoreService';

export type QualityPayload = Omit<AddImagePayload, 'base64String'>;

export async function addImageWithQuality(
  rawPath: string,
  meta: QualityPayload,
): Promise<{ success: boolean; message?: string; result?: SharpnessResult }> {
  try {
    // 1. down‑sample
    const compressedPath = await compressImageFromUri(
      rawPath,
      'JPEG',
      400,
      400,
      20,
    );
    if (!compressedPath) throw new Error('Compression failed');

    // 2. analyze sharpness
    const sharpness = await analyzeImageSharpness(compressedPath);

    // 3. convert to base64
    const base64String = await convertFileToBase64(compressedPath);
    if (!base64String) throw new Error('Base64 conversion failed');

    // 4. persist with blur metadata
    const payload: AddImagePayload = {
      ...meta,
      base64String,
      isBlurry: sharpness.isBlurry,
      sharpnessScore: sharpness.sharpnessScore,
    };
    const storeResult = await ImageStore.addImage(payload);

    return {
      success: storeResult.success,
      message: storeResult.message,
      result: sharpness,
    };
  } catch (error) {
    console.error('ImageQualityService.addImageWithQuality:', error);
    return { success: false, message: (error as Error).message };
  }
}
