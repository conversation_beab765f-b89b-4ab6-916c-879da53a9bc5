import { BlurDetectionService } from '~/services/BlurDetectionService';

/**
 * Test utility for manually testing blur detection with sample images
 * 
 * This utility helps you test the BlurDetectionService by:
 * 1. Testing with file paths (if you have sample images)
 * 2. Testing with base64 data
 * 3. Providing detailed logging of results
 * 
 * Usage examples:
 * - testBlurDetectionWithPath('/path/to/your/test/image.jpg')
 * - testBlurDetectionWithBase64('your-base64-string-here')
 * - runBlurDetectionTests() // runs a comprehensive test suite
 */

/**
 * Test blur detection with a file path
 * @param imagePath - Path to the image file to test
 * @param verbose - Whether to show detailed logging
 */
export async function testBlurDetectionWithPath(
  imagePath: string,
  verbose: boolean = true
): Promise<void> {
  console.log(`\n=== Testing Blur Detection with Path ===`);
  console.log(`Image path: ${imagePath}`);
  
  try {
    // Test the main analysis function
    const result = await BlurDetectionService.analyzeFromPath(imagePath, { verbose });
    
    console.log('\n--- Analysis Results ---');
    console.log(`Sharpness Score: ${result.sharpnessScore}`);
    console.log(`Is Blurry: ${result.isBlurry}`);
    console.log(`Confidence: ${result.confidence}`);
    console.log(`Analysis Method: ${result.analysisMethod}`);
    console.log(`Analyzed At: ${result.analyzedAt.toISOString()}`);
    
    // Test the convenience function
    const isAcceptable = await BlurDetectionService.isImageAcceptable(imagePath);
    console.log(`Is Acceptable Quality: ${isAcceptable}`);
    
    // Get human-readable description
    const description = BlurDetectionService.getQualityDescription(result);
    console.log(`Quality Description: ${description}`);
    
    console.log('\n--- Test Complete ---\n');
    
  } catch (error) {
    console.error('Error during blur detection test:', error);
  }
}

/**
 * Test blur detection with base64 data
 * @param base64Data - Base64 encoded image data
 * @param verbose - Whether to show detailed logging
 */
export async function testBlurDetectionWithBase64(
  base64Data: string,
  verbose: boolean = true
): Promise<void> {
  console.log(`\n=== Testing Blur Detection with Base64 ===`);
  console.log(`Base64 data length: ${base64Data.length} characters`);
  
  try {
    // Test the main analysis function
    const result = await BlurDetectionService.analyzeFromBase64(base64Data, { verbose });
    
    console.log('\n--- Analysis Results ---');
    console.log(`Sharpness Score: ${result.sharpnessScore}`);
    console.log(`Is Blurry: ${result.isBlurry}`);
    console.log(`Confidence: ${result.confidence}`);
    console.log(`Analysis Method: ${result.analysisMethod}`);
    console.log(`Analyzed At: ${result.analyzedAt.toISOString()}`);
    
    // Test the convenience function
    const isAcceptable = await BlurDetectionService.isBase64ImageAcceptable(base64Data);
    console.log(`Is Acceptable Quality: ${isAcceptable}`);
    
    // Get human-readable description
    const description = BlurDetectionService.getQualityDescription(result);
    console.log(`Quality Description: ${description}`);
    
    console.log('\n--- Test Complete ---\n');
    
  } catch (error) {
    console.error('Error during blur detection test:', error);
  }
}

/**
 * Run a comprehensive test suite with sample data
 * This function tests various scenarios to validate the service
 */
export async function runBlurDetectionTests(): Promise<void> {
  console.log('\n🔍 Starting Comprehensive Blur Detection Tests\n');
  
  // Test 1: Test with a small base64 image (1x1 pixel PNG)
  console.log('Test 1: Small base64 image');
  const smallBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
  await testBlurDetectionWithBase64(smallBase64, false);
  
  // Test 2: Test with empty base64
  console.log('Test 2: Empty base64 (should handle gracefully)');
  await testBlurDetectionWithBase64('', false);
  
  // Test 3: Test with invalid base64
  console.log('Test 3: Invalid base64 (should handle gracefully)');
  await testBlurDetectionWithBase64('invalid-base64-data', false);
  
  // Test 4: Test with non-existent file path
  console.log('Test 4: Non-existent file path (should handle gracefully)');
  await testBlurDetectionWithPath('/non/existent/path.jpg', false);
  
  console.log('✅ All tests completed!\n');
  console.log('📝 To test with your own images:');
  console.log('   - Use testBlurDetectionWithPath("/path/to/your/image.jpg")');
  console.log('   - Use testBlurDetectionWithBase64("your-base64-string")');
  console.log('\n💡 Integration tip:');
  console.log('   The service is now integrated into CameraViewScreen.tsx');
  console.log('   Check the console logs when taking photos to see blur analysis results');
}

/**
 * Quick test function for development
 * Call this from your development console or add it to a button for testing
 */
export async function quickBlurTest(): Promise<void> {
  console.log('🚀 Quick Blur Detection Test');
  await runBlurDetectionTests();
}

// Example usage (you can call these functions from your development environment):
// 
// import { testBlurDetectionWithPath, runBlurDetectionTests } from '~/utils/testBlurDetection';
// 
// // Test with a specific image
// testBlurDetectionWithPath('/path/to/your/test/image.jpg');
// 
// // Run comprehensive tests
// runBlurDetectionTests();
