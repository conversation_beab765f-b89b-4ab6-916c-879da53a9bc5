// 1) Mock any native‐event modules before code imports LocationService or ImageStoreService
jest.mock('react-native-background-geolocation', () => ({
  __esModule: true,
  default: {
    onLocation: jest.fn(),
    ready: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
  },
  Location: {},
}));

jest.mock('react-native-background-fetch', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(),
    finish: jest.fn(),
  },
}));

jest.mock('react-native-geolocation-service', () => ({
  __esModule: true,
  requestAuthorization: jest.fn(),
  getCurrentPosition: jest.fn((_, success) =>
    success({ coords: { latitude: 0, longitude: 0 } }),
  ),
  watchPosition: jest.fn(),
  stopObserving: jest.fn(),
}));

jest.mock('react-native-device-info', () => ({
  __esModule: true,
  getUniqueId: jest.fn(() => 'device-id'),
  getSystemName: jest.fn(() => 'iOS'),
}));

// 2) Mock LocationService so it never reaches real native modules
jest.mock('~/services/location/LocationService', () => ({
  getCurrentLocationFromGeolocation: jest.fn(async () => ({
    coords: { latitude: 0, longitude: 0 },
  })),
}));

// 3) Import the service under test
import {
  addImageWithQuality,
  QualityPayload,
} from '~/services/ImageQualityStore';
import * as imageUtils from '~/utils/images';
import * as sharpnessUtil from '~/utils/imageSharpness';
import { ImageStore } from '~/services/ImageStoreService';

jest.mock('~/utils/images');
jest.mock('~/utils/imageSharpness');
jest.mock('~/services/ImageStoreService');

describe('ImageQualityStore.addImageWithQuality', () => {
  const rawPath = '/tmp/photo.jpg';
  const meta: QualityPayload = {
    titleType: 'photoPerimeter',
    routeSummaryId: 'route123',
  };

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('flows successfully and returns store success and sharpness result', async () => {
    (imageUtils.compressImageFromUri as jest.Mock).mockResolvedValue(
      '/tmp/small.jpg',
    );

    const fakeSharp: sharpnessUtil.SharpnessResult = {
      sharpnessScore: 150,
      isBlurry: false,
      confidence: 'medium',
    };
    (sharpnessUtil.analyzeImageSharpness as jest.Mock).mockResolvedValue(
      fakeSharp,
    );

    (imageUtils.convertFileToBase64 as jest.Mock).mockResolvedValue(
      'base64string',
    );

    (ImageStore.addImage as jest.Mock).mockResolvedValue({
      success: true,
      message: 'ok',
    });

    const result = await addImageWithQuality(rawPath, meta);

    expect(imageUtils.compressImageFromUri).toHaveBeenCalledWith(
      rawPath,
      'JPEG',
      400,
      400,
      20,
    );
    expect(sharpnessUtil.analyzeImageSharpness).toHaveBeenCalledWith(
      '/tmp/small.jpg',
    );
    expect(imageUtils.convertFileToBase64).toHaveBeenCalledWith(
      '/tmp/small.jpg',
    );
    expect(ImageStore.addImage).toHaveBeenCalledWith({
      ...meta,
      base64String: 'base64string',
      isBlurry: fakeSharp.isBlurry,
      sharpnessScore: fakeSharp.sharpnessScore,
    });

    expect(result).toEqual({
      success: true,
      message: 'ok',
      result: fakeSharp,
    });
  });

  it('returns failure when compression fails', async () => {
    (imageUtils.compressImageFromUri as jest.Mock).mockResolvedValue(null);

    const result = await addImageWithQuality(rawPath, meta);

    expect(result.success).toBe(false);
    expect(result.message).toMatch(/Compression failed/);
    expect(ImageStore.addImage).not.toHaveBeenCalled();
  });

  it('returns failure when base64 conversion fails', async () => {
    (imageUtils.compressImageFromUri as jest.Mock).mockResolvedValue(
      '/tmp/small.jpg',
    );
    (sharpnessUtil.analyzeImageSharpness as jest.Mock).mockResolvedValue({
      sharpnessScore: 10,
      isBlurry: true,
      confidence: 'high',
    });
    (imageUtils.convertFileToBase64 as jest.Mock).mockResolvedValue(null);

    const result = await addImageWithQuality(rawPath, meta);

    expect(result.success).toBe(false);
    expect(result.message).toMatch(/Base64 conversion failed/);
    expect(ImageStore.addImage).not.toHaveBeenCalled();
  });
});
