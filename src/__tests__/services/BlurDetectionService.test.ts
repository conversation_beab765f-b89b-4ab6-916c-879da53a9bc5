import { BlurDetectionService, BlurDetectionResult } from '~/services/BlurDetectionService';
import * as imageSharpness from '~/utils/imageSharpness';

// Mock the imageSharpness utilities
jest.mock('~/utils/imageSharpness');

const mockAnalyzeImageSharpness = imageSharpness.analyzeImageSharpness as jest.MockedFunction<
  typeof imageSharpness.analyzeImageSharpness
>;

const mockAnalyzeImageSharpnessFromBase64 = imageSharpness.analyzeImageSharpnessFromBase64 as jest.MockedFunction<
  typeof imageSharpness.analyzeImageSharpnessFromBase64
>;

describe('BlurDetectionService', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('analyzeFromPath', () => {
    it('should analyze image from file path and return result with metadata', async () => {
      const mockSharpnessResult = {
        sharpnessScore: 150.5,
        isBlurry: false,
        confidence: 'medium' as const,
      };

      mockAnalyzeImageSharpness.mockResolvedValue(mockSharpnessResult);

      const result = await BlurDetectionService.analyzeFromPath('/path/to/image.jpg');

      expect(mockAnalyzeImageSharpness).toHaveBeenCalledWith('/path/to/image.jpg');
      expect(result).toMatchObject({
        sharpnessScore: 150.5,
        isBlurry: false,
        confidence: 'medium',
        analysisMethod: 'file_path',
      });
      expect(result.analyzedAt).toBeInstanceOf(Date);
    });

    it('should handle errors gracefully and return default blur result', async () => {
      mockAnalyzeImageSharpness.mockRejectedValue(new Error('File not found'));

      const result = await BlurDetectionService.analyzeFromPath('/invalid/path.jpg');

      expect(result).toMatchObject({
        sharpnessScore: 0,
        isBlurry: true,
        confidence: 'low',
        analysisMethod: 'file_path',
      });
      expect(result.analyzedAt).toBeInstanceOf(Date);
      expect(console.error).toHaveBeenCalledWith(
        'BlurDetectionService: Error analyzing image from path:',
        expect.any(Error)
      );
    });

    it('should log verbose information when verbose option is enabled', async () => {
      const mockSharpnessResult = {
        sharpnessScore: 200,
        isBlurry: false,
        confidence: 'high' as const,
      };

      mockAnalyzeImageSharpness.mockResolvedValue(mockSharpnessResult);

      await BlurDetectionService.analyzeFromPath('/path/to/image.jpg', { verbose: true });

      expect(console.log).toHaveBeenCalledWith(
        'BlurDetectionService: Analyzing image at path: /path/to/image.jpg'
      );
      expect(console.log).toHaveBeenCalledWith('BlurDetectionService: Analysis complete', {
        sharpnessScore: 200,
        isBlurry: false,
        confidence: 'high',
      });
    });
  });

  describe('analyzeFromBase64', () => {
    it('should analyze image from base64 data and return result with metadata', async () => {
      const mockSharpnessResult = {
        sharpnessScore: 75.2,
        isBlurry: true,
        confidence: 'high' as const,
      };

      mockAnalyzeImageSharpnessFromBase64.mockResolvedValue(mockSharpnessResult);

      const base64Data = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
      const result = await BlurDetectionService.analyzeFromBase64(base64Data);

      expect(mockAnalyzeImageSharpnessFromBase64).toHaveBeenCalledWith(base64Data);
      expect(result).toMatchObject({
        sharpnessScore: 75.2,
        isBlurry: true,
        confidence: 'high',
        analysisMethod: 'base64',
      });
      expect(result.analyzedAt).toBeInstanceOf(Date);
    });

    it('should handle errors gracefully and return default blur result', async () => {
      mockAnalyzeImageSharpnessFromBase64.mockRejectedValue(new Error('Invalid base64'));

      const result = await BlurDetectionService.analyzeFromBase64('invalid-base64');

      expect(result).toMatchObject({
        sharpnessScore: 0,
        isBlurry: true,
        confidence: 'low',
        analysisMethod: 'base64',
      });
      expect(result.analyzedAt).toBeInstanceOf(Date);
      expect(console.error).toHaveBeenCalledWith(
        'BlurDetectionService: Error analyzing image from base64:',
        expect.any(Error)
      );
    });
  });

  describe('isImageAcceptable', () => {
    it('should return true for non-blurry images', async () => {
      mockAnalyzeImageSharpness.mockResolvedValue({
        sharpnessScore: 300,
        isBlurry: false,
        confidence: 'high',
      });

      const result = await BlurDetectionService.isImageAcceptable('/path/to/sharp-image.jpg');

      expect(result).toBe(true);
    });

    it('should return false for blurry images', async () => {
      mockAnalyzeImageSharpness.mockResolvedValue({
        sharpnessScore: 50,
        isBlurry: true,
        confidence: 'high',
      });

      const result = await BlurDetectionService.isImageAcceptable('/path/to/blurry-image.jpg');

      expect(result).toBe(false);
    });
  });

  describe('isBase64ImageAcceptable', () => {
    it('should return true for non-blurry base64 images', async () => {
      mockAnalyzeImageSharpnessFromBase64.mockResolvedValue({
        sharpnessScore: 250,
        isBlurry: false,
        confidence: 'medium',
      });

      const result = await BlurDetectionService.isBase64ImageAcceptable('base64-data');

      expect(result).toBe(true);
    });

    it('should return false for blurry base64 images', async () => {
      mockAnalyzeImageSharpnessFromBase64.mockResolvedValue({
        sharpnessScore: 30,
        isBlurry: true,
        confidence: 'high',
      });

      const result = await BlurDetectionService.isBase64ImageAcceptable('base64-data');

      expect(result).toBe(false);
    });
  });

  describe('getQualityDescription', () => {
    it('should return appropriate descriptions for different blur states and confidence levels', () => {
      const testCases: Array<{
        result: Omit<BlurDetectionResult, 'analyzedAt' | 'analysisMethod'>;
        expectedDescription: string;
      }> = [
        {
          result: { sharpnessScore: 400, isBlurry: false, confidence: 'high' },
          expectedDescription: 'Image is sharp and clear (score: 400)',
        },
        {
          result: { sharpnessScore: 200, isBlurry: false, confidence: 'medium' },
          expectedDescription: 'Image appears acceptable (score: 200)',
        },
        {
          result: { sharpnessScore: 150, isBlurry: false, confidence: 'low' },
          expectedDescription: 'Image quality is uncertain (score: 150)',
        },
        {
          result: { sharpnessScore: 30, isBlurry: true, confidence: 'high' },
          expectedDescription: 'Image is clearly blurry (score: 30)',
        },
        {
          result: { sharpnessScore: 80, isBlurry: true, confidence: 'medium' },
          expectedDescription: 'Image appears blurry (score: 80)',
        },
        {
          result: { sharpnessScore: 90, isBlurry: true, confidence: 'low' },
          expectedDescription: 'Image may be blurry, but confidence is low (score: 90)',
        },
      ];

      testCases.forEach(({ result, expectedDescription }) => {
        const fullResult: BlurDetectionResult = {
          ...result,
          analyzedAt: new Date(),
          analysisMethod: 'file_path',
        };

        const description = BlurDetectionService.getQualityDescription(fullResult);
        expect(description).toBe(expectedDescription);
      });
    });
  });
});
