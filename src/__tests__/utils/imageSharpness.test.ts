import { analyzeImageSharpness } from '~/utils/imageSharpness';
import RNFS from 'react-native-fs';
import jpeg from 'jpeg-js';
import { Buffer } from 'buffer';
global.Buffer = global.Buffer || Buffer;

jest.mock('react-native-fs');
jest.mock('jpeg-js');

describe('analyzeImageSharpness', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('returns sharpnessScore=0, isBlurry=true, confidence="high" for a uniform image', async () => {
    (RNFS.readFile as jest.Mock).mockResolvedValue('dummy-base64');

    const width = 3;
    const height = 3;
    const data = new Uint8Array(width * height * 4);
    (jpeg.decode as jest.Mock).mockReturnValue({ width, height, data });

    const result = await analyzeImageSharpness('/path/to/image.jpg');

    expect(result.sharpnessScore).toBe(0);
    expect(result.isBlurry).toBe(true);
    expect(result.confidence).toBe('high');
  });

  it('returns a positive sharpnessScore and boolean isBlurry for a checkerboard pattern', async () => {
    (RNFS.readFile as jest.Mock).mockResolvedValue('dummy-base64');

    const width = 4;
    const height = 4;
    const data = new Uint8Array(width * height * 4);
    for (let i = 0; i < width * height; i++) {
      const v = (i + Math.floor(i / width)) % 2 === 0 ? 0 : 255;
      data.set([v, v, v, 255], i * 4);
    }
    (jpeg.decode as jest.Mock).mockReturnValue({ width, height, data });

    const result = await analyzeImageSharpness('/some/path.jpg');

    expect(result.sharpnessScore).toBeGreaterThan(0);
    expect(typeof result.isBlurry).toBe('boolean');
    expect(['low', 'medium', 'high']).toContain(result.confidence);
  });
});
