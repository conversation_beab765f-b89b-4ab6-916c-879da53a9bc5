{"name": "RapidMedical", "version": "2.0.0", "private": true, "scripts": {"prepare": "husky", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:coverage": "yarn test --coverage --collectCoverageFrom='src/**/*.{ts,tsx, .js}'", "start": "react-native start", "start:debug": "npx react-native start --experimental-debugger", "clean": "./scripts/clean.sh", "install-deps": "./scripts/install.sh", "android": "react-native run-android", "ios:dev": "ENVFILE=.env.dev react-native run-ios --scheme 'RapidMedicalDev'", "ios:staging": "ENVFILE=.env.staging react-native run-ios --scheme 'RapidMedicalStaging'", "ios:prod": "ENVFILE=.env react-native run-ios --scheme 'RapidMedical' --simulator='iPhone 15'", "android:dev": "ENVFILE=.env.dev react-native run-android --mode=devDebug", "android:dev-release": "ENVFILE=.env.dev react-native run-android --mode=devRelease", "android:staging": "ENVFILE=.env.staging react-native run-android --mode=stagingDebug", "android:staging-release": "ENVFILE=.env.staging react-native run-android --mode=stagingRelease", "android:prod": "ENVFILE=.env react-native run-android --mode=prodDebug", "android:prod-release": "ENVFILE=.env react-native run-android --mode=prodRelease", "bundle:ios": "react-native bundle --minify --entry-file index.js --platform ios --dev false --bundle-output ./ios/main.jsbundle --assets-dest ./ios", "postinstall": "yarn run bundle:ios"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.11", "@lodev09/react-native-exify": "^0.2.7", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/elements": "^1.3.31", "@react-navigation/material-top-tabs": "^7.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^6.11.0", "@realm/react": "^0.11.0", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@rnmapbox/maps": "10.1.36", "@sentry/react-native": "^6.10.0", "axios": "^1.7.7", "babel-plugin-module-resolver": "^5.0.2", "jpeg-js": "^0.4.4", "lint-staged": "^15.2.10", "luxon": "^3.5.0", "react": "18.3.1", "react-native": "0.75.5", "react-native-background-fetch": "^4.2.6", "react-native-background-geolocation": "^4.18.3", "react-native-config": "^1.5.3", "react-native-device-info": "^13.1.0", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.21.2", "react-native-keychain": "^9.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-onesignal": "^5.2.12", "react-native-pager-view": "^6.4.1", "react-native-permissions": "^5.1.0", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.4.0", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "^15.7.1", "react-native-tab-view": "^4.0.5", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.3", "react-native-webview": "^13.12.4", "realm": "^20.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.5", "@react-native/eslint-config": "0.75.5", "@react-native/metro-config": "0.75.5", "@react-native/typescript-config": "0.75.5", "@testing-library/react-native": "^13.0.1", "@types/jest": "^29.5.14", "@types/luxon": "^3.4.2", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^9.1.6", "jest": "^29.6.3", "prettier": "^3.0.3", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.8", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["prettier --check", "eslint"]}}